{"name": "document-chatbot-platform", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@langchain/community": "^0.2.0", "@langchain/openai": "^0.2.0", "clsx": "^2.1.0", "faiss-node": "^0.5.1", "framer-motion": "^11.1.8", "langchain": "^0.2.0", "lucide-react": "^0.344.0", "mammoth": "^1.6.0", "openai": "^4.52.0", "pdf-parse": "^1.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-hook-form": "^7.51.0", "react-router-dom": "^6.22.3", "tailwind-merge": "^2.2.1", "zod": "^3.22.4", "zustand": "^4.5.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^6.3.5"}}